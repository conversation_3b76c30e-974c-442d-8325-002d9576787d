import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios'; // Import axios

// Mock data for initial implementation - will be replaced with API call
// const mockQuizData = {
//     topic: "Topic Name Fetched from Backend",
//     totalQuestions: 200,
//     totalMarks: 200,
//     duration: 1800, // 30 minutes in seconds
//     questions: [
//         {
//             id: 1,
//             question: "The Desert National Park (DNP) is located in which state?",
//             options: ["Rajasthan", "Haryana", "Gujarat", "Assam"],
//             correctAnswer: 0
//         },
//         // Add more mock questions for testing
//         ...Array.from({ length: 200 }, (_, i) => ({
//             id: i + 2,
//             question: `Sample question ${i + 2} for testing purposes?`,
//             options: [`Option A ${i + 2}`, `Option B ${i + 2}`, `Option C ${i + 2}`, `Option D ${i + 2}`],
//             correctAnswer: Math.floor(Math.random() * 4)
//         }))
//     ]
// };

const MCQQuiz = () => {
    // State management
    const [isMobile, setIsMobile] = useState(false);
    const [quizData, setQuizData] = useState(null);
    const [currentQuestion, setCurrentQuestion] = useState(1);
    const [selectedAnswers, setSelectedAnswers] = useState({});
    const [markedForReview, setMarkedForReview] = useState(new Set());
    const [timeLeft, setTimeLeft] = useState(0);
    const [loading, setLoading] = useState(true);
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [wbStrId, setWbStrId] = useState(null);
    const [topicId, setTopicId] = useState(null);
    const [isConfigLoaded, setIsConfigLoaded] = useState(false);

    const navigate = useNavigate();
    const timerRef = useRef(null);

    // Add state for preference modal
    const [showPreferenceModal, setShowPreferenceModal] = useState(true);
    const [selectedQuestionsCount, setSelectedQuestionsCount] = useState(10); // Default or initial value
    const [selectedDuration, setSelectedDuration] = useState(''); // Placeholder for duration input
    const [topicName, setTopicName] = useState(''); // To store topic name from local storage

    // Add state for duration inputs
    const [selectedHours, setSelectedHours] = useState(0);
    const [selectedMinutes, setSelectedMinutes] = useState(30); // Default to 30 minutes
    const [selectedSeconds, setSelectedSeconds] = useState(0);

    // Hardcoded IDs for API call (replace with actual logic later)
    const hardcodedWbStrId = localStorage.getItem("wbStrId2");
    const hardcodedTopicId = localStorage.getItem("topicId");

    // Responsive detection
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < window.innerHeight);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => {
            window.removeEventListener('resize', checkMobile);
        };
    }, []);

    // Handle page reload for landscape mode
    const handleDoneReload = () => {
        window.location.reload();
    };


    useEffect(() => {
        console.log('wbStrId', localStorage.getItem("wbStrId2"));
        console.log('topicId', localStorage.getItem("topicId"));
        const a = localStorage.getItem("wbStrId2");
        const b = localStorage.getItem("topicId");
        setWbStrId(a);
        setTopicId(b);
        setIsConfigLoaded(true);
    }, []);

    // Initialize quiz data and load from localStorage
    useEffect(() => {
        // Always show the preference modal on component mount
        setShowPreferenceModal(true);

        // Load topic name from local storage
        const savedTopicName = localStorage.getItem('mcq_quiz_topic'); // Assuming topic is stored in 'mcq_quiz_topic'
        if (savedTopicName) {
            setTopicName(savedTopicName);
        } else {
            // Handle case where topic name is not in local storage
            setTopicName("Default Topic Name"); // Fallback or error handling
        }

        // Load saved state from localStorage if it exists, but don't hide the modal here
        const savedStartTime = localStorage.getItem('mcq_quiz_start_time');
        if (savedStartTime) {
            const savedAnswers = localStorage.getItem('mcq_quiz_answers');
            const savedMarkedForReview = localStorage.getItem('mcq_quiz_marked_for_review');
            const savedCurrentQuestion = localStorage.getItem('mcq_quiz_current_question');
            const startTime = parseInt(savedStartTime, 10);

            if (savedAnswers) {
                try {
                    setSelectedAnswers(JSON.parse(savedAnswers));
                } catch (error) {
                    console.error('Error parsing saved answers:', error);
                }
            }

            if (savedMarkedForReview) {
                try {
                    setMarkedForReview(new Set(JSON.parse(savedMarkedForReview)));
                } catch (error) {
                    console.error('Error parsing marked for review:', error);
                }
            }

            if (savedCurrentQuestion) {
                setCurrentQuestion(parseInt(savedCurrentQuestion, 10));
            }

            // We need to reconstruct or refetch the quiz data based on saved state to calculate timeLeft correctly
            // For now, we'll assume the quiz configuration (topic, count, duration) was also saved or can be derived.
            // A more robust implementation would save the quiz config and use it here.
            // For this example, we'll just set a loading state and potentially refetch or show a message.
            // Let's assume we need to refetch the quiz data based on saved state.

            // *** IMPORTANT ***
            // If you need to resume a specific quiz instance with saved progress,
            // you would typically save the quiz ID or parameters (topicId, noOfQuestions, duration)
            // and use those to refetch the *exact* quiz taken previously.
            // The current mock fetchQuizQuestions doesn't support this.
            // For now, we'll just set loading to false and rely on the user to start a new quiz via the modal.
            // If you want to auto-resume, we'd need to adjust the logic significantly.
            setLoading(false);

        } else {
            // If no saved state, set loading to false (ready to show modal)
            setLoading(false); // Not loading quiz data yet, waiting for preference
        }


    }, []);

    // Timer countdown - starts after loading is complete AND preference modal is closed
    useEffect(() => {
        // Clear any existing timer
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }

        // Don't start timer if still loading, no time left, or preference modal is open
        if (loading || timeLeft <= 0 || showPreferenceModal) return;

        // Start new timer
        timerRef.current = setInterval(() => {
            setTimeLeft(prev => {
                if (prev <= 1) {
                    // Auto-submit when time runs out
                    setShowSubmitModal(true);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        // Cleanup function
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [loading, showPreferenceModal]); // Restart when loading state or modal state changes

    // Save state to localStorage whenever it changes (only when quiz is active)
    useEffect(() => {
        if (!showPreferenceModal && quizData) {
            localStorage.setItem('mcq_quiz_answers', JSON.stringify(selectedAnswers));
        }
    }, [selectedAnswers, showPreferenceModal, quizData]);

    useEffect(() => {
        if (!showPreferenceModal && quizData) {
            localStorage.setItem('mcq_quiz_marked_for_review', JSON.stringify([...markedForReview]));
        }
    }, [markedForReview, showPreferenceModal, quizData]);

    useEffect(() => {
        if (!showPreferenceModal && quizData) {
            localStorage.setItem('mcq_quiz_current_question', currentQuestion.toString());
        }
    }, [currentQuestion, showPreferenceModal, quizData]);


    // Cleanup timer on component unmount
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, []);

    // Format time display
    const formatTime = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')} : ${minutes.toString().padStart(2, '0')} : ${secs.toString().padStart(2, '0')}`;
    };

    // Handle answer selection
    const handleAnswerSelect = (questionQNo, optionIndex) => {
        setSelectedAnswers(prev => ({
            ...prev,
            [questionQNo]: optionIndex
        }));
    };

    // Handle mark for review
    const handleMarkForReview = () => {
        if (!quizData) return; // Prevent action if quiz data is not loaded
        // Find the current question data using currentQuestion (1-indexed) and get its qNo
        const currentQuestionData = quizData.questions.find(q => q.qNo === currentQuestion);
        if (!currentQuestionData) return; // Should not happen if quizData is loaded

        const questionQNo = currentQuestionData.qNo;

        setMarkedForReview(prev => {
            const newSet = new Set(prev);
            if (newSet.has(questionQNo)) {
                newSet.delete(questionQNo);
            } else {
                newSet.add(questionQNo);
            }
            return newSet;
        });
    };

    // Navigation functions
    const handleSkip = () => {
        if (!quizData) return; // Prevent action if quiz data is not loaded
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleProceed = () => {
        if (!quizData) return; // Prevent action if quiz data is not loaded
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleQuestionNavigation = (questionNumber) => {
        if (!quizData) return; // Prevent action if quiz data is not loaded
        setCurrentQuestion(questionNumber);
    };

    // Get question status for styling
    const getQuestionStatus = (questionNumber) => {
        if (!quizData) return ''; // Return empty if quiz data is not loaded
        // Find the question data using questionNumber (1-indexed) and get its qNo
        const questionData = quizData.questions.find(q => q.qNo === questionNumber);
        if (!questionData) return ''; // Should not happen if quizData is loaded

        const questionQNo = questionData.qNo;

        const isAnswered = selectedAnswers.hasOwnProperty(questionQNo);
        const isMarked = markedForReview.has(questionQNo);
        const isCurrent = questionNumber === currentQuestion;

        if (isCurrent) return 'current';
        if (isMarked) return 'marked';
        if (isAnswered) return 'answered';
        return 'unanswered';
    };

    // Calculate attempted questions count
    const getAttemptedCount = () => {
        return Object.keys(selectedAnswers).length;
    };

    // Handle quiz submission
    const handleSubmitQuiz = () => {
        setShowSubmitModal(true);
    };

    const confirmSubmit = async () => {
        if (!quizData) return; // Prevent submission if quiz data is not loaded
        // Prepare submission data
        const submissionData = {
            answers: selectedAnswers,
            markedForReview: [...markedForReview],
            timeSpent: quizData.duration - timeLeft,
            submittedAt: new Date().toISOString()
        };

        try {
            // In real implementation, submit to API
            // await axios.post('/api/quiz/submit', submissionData);

            // Actual API Call for submission
            const submissionBody = {
                wbStrId: wbStrId,
                topicId: topicId,
                answers: selectedAnswers // Use the selectedAnswers state directly
            };

            console.log('Submission API Request Body:', submissionBody);

            const response = await axios.patch(`${import.meta.env.VITE_BACKEND_1_SERVER_URL}/videoData/getScore/`, submissionBody);

            console.log('Submission API Response:', response.data);

            if (response.data.success) {
                console.log('Quiz submitted successfully:', response.data);
            } else {
                console.error('Quiz submission failed:', response.data.message);
                // Optionally show an error message to the user
                alert(`Quiz submission failed: ${response.data.message || 'Unknown error'}`);
                // Re-throw the error to skip the rest of the success logic
                throw new Error('API indicated failure');
            }

            console.log('Quiz submitted:', submissionData);

            // Clear localStorage for this quiz session
            localStorage.removeItem('mcq_quiz_answers');
            localStorage.removeItem('mcq_quiz_marked_for_review');
            localStorage.removeItem('mcq_quiz_current_question');
            localStorage.removeItem('mcq_quiz_start_time');
            // Optional: remove topic name if it's specific to this quiz instance
            localStorage.removeItem('mcq_quiz_topic');

            // Navigate to results page
            // You might want to pass the score or other result data from the API response
            localStorage.setItem('mcqReportData', JSON.stringify(response.data.data));
            navigate('/quiz-report');
            // navigate('/quiz-report', { state: { submissionData, quizData, submissionResult: response.data.data } }); // Passing submissionResult
        } catch (error) {
            console.error('Failed to submit quiz:', error);
            // Check if the error is not the one we threw for API failure indication
            if (error.message !== 'API indicated failure') {
                alert('Failed to submit quiz. Please try again.');
            }
        }
    };

    // Handle Increment/Decrement for Questions Count
    const handleIncrementQuestions = () => {
        setSelectedQuestionsCount(prev => Math.min(30, prev + 1)); // Add validation if needed (e.g., max questions)
    };

    const handleDecrementQuestions = () => {
        setSelectedQuestionsCount(prev => Math.max(5, prev - 1)); // Ensure minimum 1 question
    };

    // Mock API call function - Replace with actual API integration
    const fetchQuizQuestions = async (topic, count, duration) => {
        console.log(`Fetching quiz for topic: ${topic}, count: ${count}, duration: ${duration}`);
        setQuizData(null); // Reset quiz data
        // Simulate API delay
        // await new Promise(resolve => setTimeout(resolve, 1000));

        // // Generate mock quiz data based on preferences
        // const mockQuestions = Array.from({ length: count }, (_, i) => ({
        //      id: i + 1,
        //      question: `Generated question ${i + 1} about ${topic}?`,
        //      options: [`Option A ${i + 1}`, `Option B ${i + 1}`, `Option C ${i + 1}`, `Option D ${i + 1}`],
        //      correctAnswer: Math.floor(Math.random() * 4)
        // }));

        // return {
        //     topic: topic,
        //     totalQuestions: count,
        //     totalMarks: count, // Assuming 1 mark per question
        //     duration: parseInt(duration, 10) || 1800, // Use selected duration, default to 30 mins
        //     questions: mockQuestions
        // };

        // Actual API Call
        try {
            console.log('wbStrId', localStorage.getItem("wbStrId2"));
            console.log('topicId', localStorage.getItem("topicId"));
            const response = await axios.put(`${import.meta.env.VITE_BACKEND_1_SERVER_URL}/videoData/getQuizData/`, {
                wbStrId: wbStrId,
                topicId: topicId,
                noOfQuestions: count
            });

            console.log('response of api call of quiz data', response);

            // Explicitly check for the nested structure and ensure questions is an array
            if (response.data && response.data.success && response.data.data) {
                const quizData = response.data.data;

                // **Explicit check before mapping**
                if (!Array.isArray(quizData.questions)) {
                    console.error('Expected quizData.questions to be an array, but received:', quizData.questions);
                    // Log the entire response data for more context
                    console.error('Full API response data:', response.data);
                    throw new Error('Invalid data format received from API: questions is not an array.');
                }

                // Adapt the API response format to the component's expected state structure
                const formattedQuestions = (quizData.questions ?? []).map(q => ({
                    id: q._id, // Use _id as the unique identifier for questions
                    question: q.question,
                    options: q.options,
                    correctAnswer: q.correctOption, // Assuming correctOption is the index of the correct answer
                    qNo: q.qNo // Add qNo to the formatted question object
                }));

                return {
                    topic: topic, // Use the topic name from state, as it's not in the API response
                    totalQuestions: quizData.questions.length,
                    totalMarks: quizData.questions.length, // Assuming 1 mark per question
                    duration: duration, // Use the selected duration
                    questions: formattedQuestions
                };
            } else {
                // Log the response data structure for debugging if success is true but data is malformed
                if (response.data && response.data.success) {
                    console.error('API call successful but returned unexpected data structure:', response.data);
                } else {
                    console.error('API request failed:', response.data);
                }
                throw new Error(response.data?.message || 'Failed to fetch quiz data or received incorrect data format');
            }

        } catch (error) {
            console.error('Error fetching quiz data:', error);
            throw error; // Re-throw to be caught by handleStartQuiz
        }

    };


    // Handle Start Quiz button click
    const handleStartQuiz = async () => {
        const totalDurationInSeconds = (selectedHours * 3600) + (selectedMinutes * 60) + selectedSeconds;
        localStorage.setItem("mcq_quiz_duration_for_result", totalDurationInSeconds.toString());
        if (!topicName || selectedQuestionsCount <= 0 || totalDurationInSeconds <= 0) {
            alert("Please select quiz preferences (topic, number of questions, and duration).");
            return;
        }

        setLoading(true);
        setShowPreferenceModal(false);

        // Clear any previous quiz state
        localStorage.removeItem('mcq_quiz_answers');
        localStorage.removeItem('mcq_quiz_marked_for_review');
        localStorage.removeItem('mcq_quiz_current_question');
        localStorage.removeItem('mcq_quiz_start_time');

        let attempt = 0;
        let fetchedQuizData = null;

        while (attempt < 2) {
            try {
                attempt += 1;

                // ⏳ Attempt fetch
                const response = await fetchQuizQuestions(topicName, selectedQuestionsCount, totalDurationInSeconds);
                console.log(`Fetch attempt ${attempt}:`, response); // 🔍 DEBUG

                if (!response || !Array.isArray(response.questions) || typeof response.duration !== 'number') {
                    throw new Error("Invalid quiz data format.");
                }

                fetchedQuizData = response;
                break; // ✅ Successfully fetched data, exit loop
            } catch (error) {
                console.error(`Attempt ${attempt} failed:`, error);

                if (attempt >= 2) {
                    alert(`Failed to start quiz. Please try again.\n${error.message}`);
                    setShowPreferenceModal(true);
                    setQuizData(null);
                    setTimeLeft(0);
                }
            }
        }

        // Proceed only if data is fetched
        if (fetchedQuizData) {
            setQuizData(fetchedQuizData);
            setTimeLeft(fetchedQuizData.duration);
            localStorage.setItem('mcq_quiz_start_time', Date.now().toString());
            setCurrentQuestion(1);
            setSelectedAnswers({});
            setMarkedForReview(new Set());
        }

        setLoading(false);
    };



    if (loading) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className='flex flex-col items-center gap-4'>
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3A1078]"></div>
                    <p className="text-xl text-[#3A1078] font-medium font-poppins">
                        Please wait while your quiz is being generated.
                    </p>
                    <p className="text-xl text-[#ce4646] font-medium font-poppins">
                        Do not refresh or press back button. Redirecting from this page will erase your quiz progress.
                    </p>
                </div>
            </div>
        );
    }

    // Mobile landscape orientation check - show this for mobile portrait mode regardless of other states
    if (isMobile && !loading) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center p-6">
                <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 shadow-lg text-center">
                    <div className="mb-6">
                        <div className="text-6xl mb-4">📱</div>
                        <h2 className="text-2xl font-bold text-[#3A1078] mb-4 font-poppins">
                            Rotate Your Device
                        </h2>
                        <p className="text-lg text-gray-700 mb-6 font-poppins">
                            To continue to give exam please rotate your device or make it landscape mode
                        </p>
                    </div>
                    <button
                        onClick={handleDoneReload}
                        className="w-full py-3 bg-gradient-to-r from-[#9825FF] to-[#2F36FF] text-white rounded-lg font-bold text-lg font-poppins hover:from-[#5A2083] hover:to-[#9034EF] transition-colors shadow-md"
                    >
                        Done
                    </button>
                </div>
            </div>
        );
    }

    // Render preference modal if visible
    if (showPreferenceModal) {
        return (
            <div className="fixed inset-0 bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-8 w-[550px] mx-4 shadow-lg">
                    <div >
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-2xl font-bold text-[#3A1078] font-poppins">Select Your Preference</h3>
                            {/* Add a close button if needed */}
                            <button onClick={() => setShowPreferenceModal(false)} className="text-gray-500 hover:text-gray-700 text-2xl font-bold">&times;</button>
                        </div>
                    </div>
                    <div className="space-y-6">
                        <div>
                            <label className="block text-lg font-medium text-gray-700 mb-2 font-poppins">Topic Name</label>
                            <input
                                type="text"
                                value={topicName}
                                readOnly // Topic name is not editable
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-600 font-jost"
                                placeholder="Topic Name (Fixed Fetched from Backend)"
                            />
                        </div>

                        <div className="flex items-center justify-between gap-4">
                            <div>
                                <label className="block text-lg font-medium text-gray-700 mb-2 font-poppins">Select No of Questions</label>
                                <div className="w-48 flex items-center justify-between border border-gray-300 rounded-lg">
                                    <button
                                        onClick={handleDecrementQuestions}
                                        className="max-w-1/3 px-6 py-2 border-r-1 border-gray-300 text-gray-700 rounded-l-lg hover:bg-gray-200 font-bold text-xl"
                                    >
                                        -
                                    </button>
                                    <input
                                        type="text"
                                        value={selectedQuestionsCount}
                                        readOnly
                                        className="max-w-1/3 text-center border-gray-300 py-2 text-lg font-jost"
                                    />
                                    <button
                                        onClick={handleIncrementQuestions}
                                        className="max-w-1/3 px-6 py-2 border-l-1 border-gray-300 text-gray-700 rounded-r-lg hover:bg-gray-200 font-bold text-xl"
                                    >
                                        +
                                    </button>
                                </div>
                            </div>

                            <div>
                                <label className="block text-lg font-medium text-gray-700 mb-2 font-poppins">Choose Time</label>
                                {/* Placeholder for Duration Picker - Replace with a proper component */}
                                {/* <input
                                    type="number"
                                    value={selectedDuration}
                                    onChange={(e) => setSelectedDuration(e.target.value)}
                                     className="w-full px-4 py-3 border border-gray-300 rounded-lg text-lg font-jost"
                                     placeholder="e.g., 1800"
                                /> */}
                                {/* <span className="text-sm text-gray-500 mt-1 block">Enter duration in seconds (e.g., 1800 for 30 minutes)</span> */}
                                <div className="flex items-center gap-2 mr-2">
                                    {/* <input
                                        type="number"
                                        value={selectedHours}
                                        onChange={(e) => setSelectedHours(parseInt(e.target.value) || 0)}
                                        className="w-16 px-2 py-2 border border-gray-300 rounded-lg text-center text-lg font-jost"
                                        placeholder="HH"
                                        min="0"
                                    />
                                    <span className="text-lg font-bold">:</span> */}
                                    <div className="flex items-center space-x-2">
                                        {/* Minutes Input */}
                                        <div className="flex items-center space-x-1">
                                            <input
                                                type="number"
                                                value={selectedMinutes}
                                                onChange={(e) => setSelectedMinutes(parseInt(e.target.value) || 0)}
                                                className="w-16 px-2 py-2 border border-gray-300 rounded-lg text-center text-lg font-jost"
                                                placeholder="MM"
                                                min="0"
                                                max="59"
                                            />
                                            <span className="text-lg font-normal">m</span>
                                        </div>

                                        {/* Separator */}
                                        {/* <span className="text-lg font-bold">:</span> */}

                                        {/* Seconds Input */}
                                        <div className="flex items-center space-x-1">
                                            <input
                                                type="number"
                                                value={selectedSeconds}
                                                onChange={(e) => setSelectedSeconds(parseInt(e.target.value) || 0)}
                                                className="w-16 px-2 py-2 border border-gray-300 rounded-lg text-center text-lg font-jost"
                                                placeholder="SS"
                                                min="0"
                                                max="59"
                                            />
                                            <span className="text-lg font-normal">s</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button
                            onClick={handleStartQuiz}
                            disabled={!isConfigLoaded || !topicName || selectedQuestionsCount <= 0 || ((selectedHours * 3600) + (selectedMinutes * 60) + selectedSeconds) <= 0}
                            className={`w-full py-4 bg-gradient-to-r from-[#9825FF] to-[#2F36FF] text-white rounded-lg font-bold text-lg font-poppins transition-colors shadow-md
                                ${!isConfigLoaded || !topicName || selectedQuestionsCount <= 0 || ((selectedHours * 3600) + (selectedMinutes * 60) + selectedSeconds) <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:from-[#5A2083] hover:to-[#9034EF]'}`}
                        >
                            Start Quiz
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    const currentQuestionData = quizData.questions[currentQuestion - 1];

    return (
        <div className="min-h-screen bg-[#F9FDFF] p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-medium text-[#3A1078] mt-6 mb-6 font-poppins">
                        Topic: {quizData.topic}
                    </h1>
                    <p className="text-xl text-[#3A1078] mt-6 font-medium font-poppins">
                        Choose the correct option, and submit your quiz after reviewing all questions.
                    </p>
                    <p className="text-m text-[#c44b4b] mt-6 font-medium font-poppins">
                        ⚠ Note: Refreshing this page will erase your quiz progress.
                    </p>
                </div>

                {/* Main Quiz Interface */}
                <div className="flex gap-8 mt-16">
                    {/* Left Section - Question Display */}
                    <div className="flex-1">
                        <div className="bg-white rounded-[38px] shadow-lg overflow-hidden">
                            {/* Question Header */}
                            <div className="bg-[#4C60A5] text-white px-6 py-4 flex justify-between items-center">
                                <h2 className="text-3xl font-medium font-poppins">Question {currentQuestion}</h2>
                                <span className="text-lg font-medium font-poppins">{currentQuestion}/{quizData.totalQuestions}</span>
                            </div>

                            {/* Question Content */}
                            <div className="px-8 pt-8">
                                <h3 className="text-2xl font-medium text-black mb-8 font-advent-pro">
                                    {currentQuestionData.question}
                                </h3>

                                {/* Options */}
                                <div className="space-y-4">
                                    {currentQuestionData.options.map((option, index) => {
                                        const isSelected = selectedAnswers[currentQuestionData.qNo] === index;
                                        return (
                                            <div key={index} className="flex items-center gap-4">
                                                <div className="relative">
                                                    <div className={`w-6 h-6 rounded-full border-3 ${isSelected ? 'border-black' : 'border-gray-400'}`}>
                                                        {isSelected && (
                                                            <div className="w-3.5 h-3.5 bg-[#4182F9] rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                                                        )}
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => handleAnswerSelect(currentQuestionData.qNo, index)}
                                                    className="text-xl font-medium text-black font-advent-pro text-left hover:text-[#4182F9] transition-colors"
                                                >
                                                    {option}
                                                </button>
                                            </div>
                                        );
                                    })}
                                </div>

                                {/* Navigation Buttons */}
                                <div className="flex justify-between gap-4 mt-16">
                                    <button
                                        onClick={handleSkip}
                                        className="px-8 py-3 bg-[#342499] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#2A1A7A] transition-colors"
                                    >
                                        Skip
                                    </button>
                                    <div className='flex gap-4'>
                                        <button
                                            onClick={handleMarkForReview}
                                            className={`px-8 py-3 rounded-lg font-bold text-base font-poppins transition-colors ${markedForReview.has(currentQuestionData.qNo)
                                                ? 'bg-[#D4A017] text-white hover:bg-[#B8900F]'
                                                : 'bg-[#AE7A01] text-white hover:bg-[#8B6001]'
                                                }`}
                                        >
                                            {markedForReview.has(currentQuestionData.qNo) ? 'Unmark Review' : 'Mark For Review'}
                                        </button>
                                        <button
                                            onClick={handleProceed}
                                            disabled={currentQuestion >= quizData.totalQuestions}
                                            className="px-8 py-3 bg-[#217C58] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#1A6347] disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                        >
                                            Proceed
                                        </button>
                                    </div>
                                </div>


                            </div>
                            {/* Bottom Info */}
                            <div className='bg-[#4C60A5] w-full pb-4'>
                                <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Number Of Questions: {quizData.totalQuestions}
                                    </span>
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Marks: {quizData.totalMarks}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Section - Timer and Navigation */}
                    <div className="w-90">
                        <div className="bg-white rounded-lg shadow-lg p-6 space-y-6 border-2 border-gray-200">
                            {/* Timer */}
                            <div className="text-center">
                                <h3 className="text-xl font-bold text-black mb-2 font-jost">Time Left</h3>
                                <div
                                    className={`text-4xl font-extrabold font-jost ${timeLeft <= 60
                                        ? 'text-red-600'
                                        : timeLeft <= 120
                                            ? 'text-yellow-600'
                                            : 'text-[#08A064]'
                                        }`}
                                >
                                    {formatTime(timeLeft)}
                                </div>
                            </div>


                            {/* Attempted Count */}
                            <div className="text-center">
                                <p className="text-xl font-semibold text-black font-poppins">
                                    Attempted: {getAttemptedCount()}/{quizData.totalQuestions}
                                </p>
                            </div>

                            {/* Question Navigation Grid */}
                            <div className="h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200">
                                <div className="space-y-3 pr-2">
                                    {Array.from({ length: Math.ceil(quizData.totalQuestions / 6) }, (_, rowIndex) => (
                                        <div key={rowIndex} className="flex gap-2">
                                            {Array.from({ length: 6 }, (_, colIndex) => {
                                                const questionNumber = rowIndex * 6 + colIndex + 1;
                                                if (questionNumber > quizData.totalQuestions) return null;

                                                const status = getQuestionStatus(questionNumber);
                                                const getButtonStyle = () => {
                                                    switch (status) {
                                                        case 'current':
                                                            return 'bg-[#4D8BD7] text-white'; //  for current
                                                        case 'answered':
                                                            return 'bg-[#217C58] text-white'; // Green for answered
                                                        case 'marked':
                                                            return 'bg-[#AE7A01] text-white'; // Orange for marked
                                                        default:
                                                            return 'bg-[#5C5C5D] text-white'; // Gray for unanswered
                                                    }
                                                };

                                                return (
                                                    <button
                                                        key={questionNumber}
                                                        onClick={() => handleQuestionNavigation(questionNumber)}
                                                        className={`w-12 h-12 rounded-lg font-normal text-xl font-prompt ${getButtonStyle()} hover:opacity-80 transition-opacity shadow-md`}
                                                    >
                                                        {questionNumber}
                                                    </button>
                                                );
                                            })}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                onClick={handleSubmitQuiz}
                                className="w-full py-4 bg-[#005FD0] text-white rounded-lg font-medium text-lg font-afacad hover:bg-[#004BB0] transition-colors shadow-md"
                            >
                                Confirm and Submit
                            </button>
                        </div>
                    </div>
                </div>

                {/* Submit Confirmation Modal */}
                {showSubmitModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                            <h3 className="text-xl font-bold text-[#3A1078] mb-4">Confirm Submission</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to submit your quiz? You have answered {getAttemptedCount()} out of {quizData.totalQuestions} questions.
                            </p>
                            <div className="flex gap-4">
                                <button
                                    onClick={() => setShowSubmitModal(false)}
                                    className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={confirmSubmit}
                                    className="flex-1 px-4 py-2 bg-[#005FD0] text-white rounded-lg hover:bg-[#004BB0] transition-colors"
                                >
                                    Submit Quiz
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MCQQuiz;